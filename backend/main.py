from fastapi import FastAP<PERSON>, HTTPException, Depends, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
from typing import List
from sqlalchemy import create_engine, Column, Integer, String
from sqlalchemy.orm import sessionmaker, declarative_base, Session

DATABASE_URL = "sqlite:///./ca_program.db"

engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3004", "http://localhost:3003", "http://localhost:3000"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database models
class CARegistration(Base):
    __tablename__ = "ca_registrations"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    mobile = Column(String, nullable=False)
    college = Column(String, nullable=False)
    branch = Column(String, nullable=False)
    year_of_study = Column(String, nullable=False)
    linkedin = Column(String, nullable=True)
    instagram = Column(String, nullable=True)
    ca_id = Column(String, unique=True, index=True, nullable=True)
    status = Column(String, default="pending")  # pending, active, rejected
    referral_code = Column(String, nullable=True)  # Who referred this CA
    referral_count = Column(Integer, default=0)  # How many people this CA referred
    created_at = Column(String, nullable=True)

Base.metadata.create_all(bind=engine)

# Pydantic schemas
class CARegistrationCreate(BaseModel):
    name: str
    email: EmailStr
    mobile: str
    college: str
    branch: str
    year_of_study: str
    linkedin: str | None = None
    instagram: str | None = None
    referral_code: str | None = None

class CARegistrationOut(BaseModel):
    id: int
    name: str
    email: EmailStr
    mobile: str
    college: str
    branch: str
    year_of_study: str
    linkedin: str | None = None
    instagram: str | None = None
    ca_id: str | None = None
    status: str = "pending"
    referral_code: str | None = None
    referral_count: int = 0
    created_at: str | None = None

    class Config:
        from_attributes = True

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Campus Ambassador Registration endpoint
@app.post("/ca/register", response_model=CARegistrationOut)
def register_ca(ca: CARegistrationCreate, db: Session = Depends(get_db)):
    from datetime import datetime

    existing = db.query(CARegistration).filter(CARegistration.email == ca.email).first()
    if existing:
        raise HTTPException(status_code=400, detail="Email already registered")

    # Generate CA ID
    ca_count = db.query(CARegistration).count()
    ca_id = f"CA{ca_count + 1:03d}"

    # Update referrer's count if referral code provided
    if ca.referral_code:
        referrer = db.query(CARegistration).filter(CARegistration.ca_id == ca.referral_code).first()
        if referrer:
            referrer.referral_count += 1

    new_ca = CARegistration(
        name=ca.name,
        email=ca.email,
        mobile=ca.mobile,
        college=ca.college,
        branch=ca.branch,
        year_of_study=ca.year_of_study,
        linkedin=ca.linkedin,
        instagram=ca.instagram,
        ca_id=ca_id,
        referral_code=ca.referral_code,
        created_at=datetime.now().isoformat()
    )
    db.add(new_ca)
    db.commit()
    db.refresh(new_ca)
    return new_ca

# Legacy registration endpoint (for backward compatibility)
@app.post("/register", response_model=CARegistrationOut)
def register_legacy(ca: CARegistrationCreate, db: Session = Depends(get_db)):
    return register_ca(ca, db)

# Admin login endpoint (simple hardcoded check with form data)
@app.post("/admin/login")
def admin_login(userid: str = Form(...), password: str = Form(...)):
    if userid == "admin" and password == "admin123":
        return {"message": "Login successful"}
    else:
        raise HTTPException(status_code=401, detail="Invalid credentials")

# Get all registrations (admin only)
@app.get("/admin/registrations", response_model=List[CARegistrationOut])
def get_registrations(db: Session = Depends(get_db)):
    return db.query(CARegistration).all()

# Update CA status (admin only)
@app.put("/admin/ca/{ca_id}/status")
def update_ca_status(ca_id: int, status_data: dict, db: Session = Depends(get_db)):
    ca = db.query(CARegistration).filter(CARegistration.id == ca_id).first()
    if not ca:
        raise HTTPException(status_code=404, detail="Campus Ambassador not found")

    ca.status = status_data.get("status", "pending")
    db.commit()
    return {"message": "Status updated successfully"}

# Get CA statistics (admin only)
@app.get("/admin/stats")
def get_ca_stats(db: Session = Depends(get_db)):
    total_cas = db.query(CARegistration).count()
    active_cas = db.query(CARegistration).filter(CARegistration.status == "active").count()
    pending_cas = db.query(CARegistration).filter(CARegistration.status == "pending").count()
    total_referrals = db.query(CARegistration).with_entities(
        db.func.sum(CARegistration.referral_count)
    ).scalar() or 0

    return {
        "total_cas": total_cas,
        "active_cas": active_cas,
        "pending_cas": pending_cas,
        "total_referrals": total_referrals
    }
