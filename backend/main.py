from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
from typing import List
from sqlalchemy import create_engine, Column, Integer, String
from sqlalchemy.orm import sessionmaker, declarative_base, Session

DATABASE_URL = "sqlite:///./ca_program.db"

engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3003", "http://localhost:3000"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database model
class CARegistration(Base):
    __tablename__ = "ca_registrations"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    mobile = Column(String, nullable=False)
    college = Column(String, nullable=False)
    branch = Column(String, nullable=False)
    year_of_study = Column(String, nullable=False)

Base.metadata.create_all(bind=engine)

# Pydantic schemas
class CARegistrationCreate(BaseModel):
    name: str
    email: EmailStr
    mobile: str
    college: str
    branch: str
    year_of_study: str

class CARegistrationOut(BaseModel):
    id: int
    name: str
    email: EmailStr
    mobile: str
    college: str
    branch: str
    year_of_study: str

    class Config:
        orm_mode = True

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Registration endpoint
@app.post("/register", response_model=CARegistrationOut)
def register_ca(ca: CARegistrationCreate, db: Session = Depends(get_db)):
    existing = db.query(CARegistration).filter(CARegistration.email == ca.email).first()
    if existing:
        raise HTTPException(status_code=400, detail="Email already registered")
    new_ca = CARegistration(
        name=ca.name,
        email=ca.email,
        mobile=ca.mobile,
        college=ca.college,
        branch=ca.branch,
        year_of_study=ca.year_of_study
    )
    db.add(new_ca)
    db.commit()
    db.refresh(new_ca)
    return new_ca

# Admin login endpoint (simple hardcoded check with form data)
@app.post("/admin/login")
def admin_login(userid: str = Form(...), password: str = Form(...)):
    if userid == "admin" and password == "admin123":
        return {"message": "Login successful"}
    else:
        raise HTTPException(status_code=401, detail="Invalid credentials")

# Get all registrations (admin only)
@app.get("/admin/registrations", response_model=List[CARegistrationOut])
def get_registrations(db: Session = Depends(get_db)):
    return db.query(CARegistration).all()
