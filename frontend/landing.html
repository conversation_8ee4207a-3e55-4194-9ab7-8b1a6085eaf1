<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Campus Ambassador Program - Secura Trainings</title>
<style>
  /* Reset and base */
  *, *::before, *::after {
    box-sizing: border-box;
  }
  body {
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f9fafb;
    color: #2c3e50;
    scroll-behavior: smooth;
  }
  a {
    color: #1d4ed8;
    text-decoration: none;
  }
  a:hover {
    text-decoration: underline;
  }
  /* Container */
  .container {
    max-width: 1100px;
    margin: auto;
    padding: 0 1rem;
  }
  /* Navigation */
  nav {
    background: white;
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
  }
  nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1d4ed8;
  }
  .nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
  }
  .admin-link {
    background: #ef4444;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    transition: background 0.3s;
  }
  .admin-link:hover {
    background: #dc2626;
    text-decoration: none;
  }
  /* Hero Section */
  header.hero {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    padding: 8rem 1rem 4rem;
    text-align: center;
    margin-top: 70px;
  }
  header.hero h1 {
    font-size: 3rem;
    margin: 0 0 0.3rem;
    font-weight: 700;
    letter-spacing: 0.05em;
  }
  header.hero p {
    font-size: 1.4rem;
    margin: 0 0 2rem;
    font-weight: 600;
    letter-spacing: 0.02em;
  }
  .btn-primary {
    background-color: #fbbf24;
    color: #1e293b;
    font-weight: 600;
    border: none;
    padding: 1rem 2.5rem;
    border-radius: 9999px;
    font-size: 1.2rem;
    cursor: pointer;
    box-shadow: 0 8px 15px rgba(251,191,36,0.3);
    transition: background-color 0.3s ease;
  }
  .btn-primary:hover,
  .btn-primary:focus {
    background-color: #f59e0b;
    outline: none;
    box-shadow: 0 0 10px #f59e0b;
  }
  /* Sections */
  section {
    padding: 3rem 0;
  }
  section h2 {
    font-size: 2.2rem;
    margin-bottom: 1rem;
    color: #1e293b;
    text-align: center;
  }
  /* Program Duration */
  .duration {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2563eb;
    text-align: center;
  }
  /* Features grid */
  .features {
    display: grid;
    grid-template-columns: repeat(auto-fit,minmax(250px,1fr));
    gap: 2rem;
  }
  .feature-card {
    background: white;
    border-radius: 0.75rem;
    padding: 2rem 1.5rem;
    box-shadow: 0 4px 8px rgb(0 0 0 / 0.05);
    text-align: center;
    transition: transform 0.3s ease;
    cursor: default;
  }
  .feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgb(0 0 0 / 0.15);
  }
  .feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #3b82f6;
  }
  .feature-title {
    font-weight: 700;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #1e293b;
  }
  /* Content lists */
  .content-list {
    max-width: 700px;
    margin: 0 auto;
    list-style: none;
    padding-left: 0;
    font-size: 1.1rem;
    color: #334155;
    line-height: 1.7;
  }
  .content-list li {
    padding-left: 1.5em;
    position: relative;
    margin-bottom: 1rem;
  }
  .content-list li::before {
    content: "✔";
    position: absolute;
    left: 0;
    color: #22c55e;
    font-weight: 700;
  }
  /* Rewards list */
  .rewards-list {
    max-width: 700px;
    margin: 0 auto 2rem;
    list-style: none;
    padding-left: 0;
    font-size: 1.1rem;
    color: #334155;
    line-height: 1.7;
  }
  .rewards-list li {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.6rem;
  }
  .rewards-list li span.icon {
    font-size: 1.3rem;
    color: #f97316;
  }
  /* Eligibility */
  .eligibility-list {
    max-width: 700px;
    margin: 0 auto;
    list-style: disc inside;
    font-size: 1.1rem;
    color: #334155;
  }
  /* Application Section */
  #application {
    text-align: center;
  }
  #application p.intro {
    margin-bottom: 2rem;
    font-size: 1.2rem;
    color: #475569;
  }
  /* FAQ */
  .faq {
    max-width: 800px;
    margin: 0 auto;
  }
  .faq-item {
    background: white;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 6px rgb(0 0 0 / 0.07);
  }
  .faq-question {
    padding: 1rem 1.5rem;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    user-select: none;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
  }
  .faq-question::after {
    content: "+";
    font-weight: 900;
    position: absolute;
    right: 1.5rem;
    transition: transform 0.3s ease;
  }
  .faq-item.open .faq-question::after {
    content: "-";
    transform: rotate(180deg);
  }
  .faq-answer {
    max-height: 0;
    padding: 0 1.5rem;
    overflow: hidden;
    transition: max-height 0.35s ease, padding 0.35s ease;
    font-size: 1rem;
    color: #475569;
  }
  .faq-item.open .faq-answer {
    padding: 1rem 1.5rem;
    max-height: 300px;
  }
  /* Footer */
  footer {
    padding: 2rem 1rem;
    text-align: center;
    font-size: 0.9rem;
    color: #94a3b8;
  }
  /* Responsive */
  @media (max-width: 600px) {
    .nav-links {
      gap: 1rem;
    }
    header.hero h1 {
      font-size: 2.4rem;
    }
    header.hero p {
      font-size: 1.1rem;
    }
    .btn-primary {
      font-size: 1rem;
      padding: 0.8rem 2rem;
    }
    .features {
      grid-template-columns: 1fr;
    }
  }
</style>
</head>
<body>

<nav>
  <div class="container">
    <div class="logo">Secura Trainings</div>
    <div class="nav-links">
      <a href="admin.html" class="admin-link">Admin Login</a>
    </div>
  </div>
</nav>

<header class="hero">
  <div class="container">
    <h1>Become a Campus Ambassador</h1>
    <p>Promote. Earn. Grow.</p>
    <button class="btn-primary" onclick="window.location.href='register.html'">
      Apply Now
    </button>
  </div>
</header>

<main>
  <section id="duration" class="container">
    <h2>Program Duration</h2>
    <p class="duration">Ambassadors are onboarded for a <strong>1-year program</strong>.</p>
  </section>

  <section id="features" class="container">
    <h2>Key Features</h2>
    <div class="features">
      <article class="feature-card">
        <div class="feature-icon">🎓</div>
        <h3 class="feature-title">Represent Our Brand</h3>
        <p>Represent our brand at your college and promote our values.</p>
      </article>
      <article class="feature-card">
        <div class="feature-icon">📣</div>
        <h3 class="feature-title">Unique Referral Link</h3>
        <p>Get a unique referral link to share with friends and peers.</p>
      </article>
      <article class="feature-card">
        <div class="feature-icon">💸</div>
        <h3 class="feature-title">Earn Cash Incentives</h3>
        <p>Earn cash incentives for every successful registration via your link.</p>
      </article>
      <article class="feature-card">
        <div class="feature-icon">📦</div>
        <h3 class="feature-title">Promotional Perks & Events</h3>
        <p>Receive regular promotional perks and exciting event opportunities.</p>
      </article>
      <article class="feature-card">
        <div class="feature-icon">🤝</div>
        <h3 class="feature-title">Network & Connect</h3>
        <p>Network with students and professionals across campuses.</p>
      </article>
    </div>
  </section>

  <section id="responsibilities" class="container">
    <h2>Responsibilities</h2>
    <ul class="content-list">
      <li>Promote our programs online and offline</li>
      <li>Share the referral link and encourage registrations</li>
      <li>Help organize or support online campaigns and events</li>
    </ul>
  </section>

  <section id="rewards" class="container">
    <h2>Rewards &amp; Recognition</h2>
    <ul class="rewards-list">
      <li><span class="icon">💸</span> Cash incentives for each successful sign-up through your referral link</li>
      <li><span class="icon">🎖️</span> Internship Certificate at year-end</li>
      <li><span class="icon">💰</span> Additional bonuses for high performers</li>
      <li><span class="icon">🧢</span> Branded swag and gifts throughout the year</li>
    </ul>
  </section>

  <section id="eligibility" class="container">
    <h2>Eligibility Criteria</h2>
    <ul class="eligibility-list">
      <li>Currently enrolled UG/PG student</li>
      <li>Active on social media and campus activities</li>
      <li>Committed to a 1-year engagement</li>
    </ul>
  </section>

  <section id="application" class="container">
    <h2>Ready to Join?</h2>
    <p class="intro">Apply now to become a part of the Secura Trainings Campus Ambassador Program and start your journey!</p>
    <button class="btn-primary" onclick="window.location.href='register.html'">Apply Now</button>
  </section>

  <section id="faq" class="container">
    <h2>Frequently Asked Questions</h2>
    <div class="faq">
      <div class="faq-item">
        <button class="faq-question">
          What is the time commitment for the Campus Ambassador Program?
        </button>
        <div class="faq-answer" hidden>
          <p>Ambassadors are expected to actively participate for 1 year, engaging in promotions and events.</p>
        </div>
      </div>
      <div class="faq-item">
        <button class="faq-question">
          How do I get my unique referral link?
        </button>
        <div class="faq-answer" hidden>
          <p>Once accepted, you will be provided with a unique referral link to share with your network.</p>
        </div>
      </div>
      <div class="faq-item">
        <button class="faq-question">
          Are there any prerequisites to apply?
        </button>
        <div class="faq-answer" hidden>
          <p>You must be a currently enrolled UG/PG student, active on campus, and able to commit for 1 year.</p>
        </div>
      </div>
    </div>
  </section>
</main>

<footer>
  &copy; 2025 Secura Trainings and Compliances. All rights reserved.
</footer>

<script>
  // FAQ toggle functionality
  const faqItems = document.querySelectorAll('.faq-item');
  faqItems.forEach(item => {
    const questionButton = item.querySelector('.faq-question');
    const answer = item.querySelector('.faq-answer');
    questionButton.addEventListener('click', () => {
      const isOpen = item.classList.contains('open');
      if (isOpen) {
        item.classList.remove('open');
        answer.setAttribute('hidden', '');
      } else {
        item.classList.add('open');
        answer.removeAttribute('hidden');
      }
    });
  });

  // Check for referral parameter
  const urlParams = new URLSearchParams(window.location.search);
  const ref = urlParams.get('ref');
  if (ref) {
    localStorage.setItem('referralCode', ref);
    console.log('Referral code stored:', ref);
  }
</script>

</body>
</html>
