<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Campus Ambassador Program</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .tab-buttons {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
        }
        .tab-button {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        .tab-button.active {
            border-bottom-color: #007bff;
            color: #007bff;
            font-weight: bold;
        }
        .tab-button:hover {
            background-color: #f8f9fa;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #007bff;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background: #0056b3;
        }
        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: 500;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        h2 {
            color: #333;
            margin-bottom: 20px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Campus Ambassador Program</h1>
        
        <div class="tab-buttons">
            <button class="tab-button active" onclick="showTab('register')">Student Registration</button>
            <button class="tab-button" onclick="showTab('admin')">Admin Login</button>
        </div>

        <div id="register-tab">
            <div id="register-app"></div>
        </div>

        <div id="admin-tab" class="hidden">
            <div id="admin-app"></div>
        </div>
    </div>

    <script type="text/babel">
        const { useState } = React;

        // Student Registration Component
        function CARegister() {
            const [name, setName] = useState('');
            const [email, setEmail] = useState('');
            const [mobile, setMobile] = useState('');
            const [college, setCollege] = useState('');
            const [branch, setBranch] = useState('');
            const [yearOfStudy, setYearOfStudy] = useState('');
            const [message, setMessage] = useState('');

            const handleSubmit = async (e) => {
                e.preventDefault();
                setMessage('');
                
                try {
                    const res = await fetch('http://localhost:8000/register', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ 
                            name, 
                            email, 
                            mobile, 
                            college, 
                            branch, 
                            year_of_study: yearOfStudy 
                        }),
                    });
                    
                    const data = await res.json();
                    
                    if (res.ok) {
                        setMessage('Registration successful! Welcome to the Campus Ambassador Program.');
                        // Clear form
                        setName('');
                        setEmail('');
                        setMobile('');
                        setCollege('');
                        setBranch('');
                        setYearOfStudy('');
                    } else {
                        setMessage(data.detail || 'Registration failed');
                    }
                } catch (error) {
                    setMessage('Network error: ' + error.message);
                }
            };

            return (
                <div>
                    <h2>Join as Campus Ambassador</h2>
                    <form onSubmit={handleSubmit}>
                        <div className="form-group">
                            <label htmlFor="name">Full Name</label>
                            <input
                                id="name"
                                type="text"
                                placeholder="Enter your full name"
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                                required
                            />
                        </div>
                        
                        <div className="form-group">
                            <label htmlFor="email">Email Address</label>
                            <input
                                id="email"
                                type="email"
                                placeholder="Enter your email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                required
                            />
                        </div>
                        
                        <div className="form-group">
                            <label htmlFor="mobile">Mobile Number</label>
                            <input
                                id="mobile"
                                type="tel"
                                placeholder="Enter your mobile number"
                                value={mobile}
                                onChange={(e) => setMobile(e.target.value)}
                                required
                            />
                        </div>
                        
                        <div className="form-group">
                            <label htmlFor="college">College/University</label>
                            <input
                                id="college"
                                type="text"
                                placeholder="Enter your college name"
                                value={college}
                                onChange={(e) => setCollege(e.target.value)}
                                required
                            />
                        </div>
                        
                        <div className="form-group">
                            <label htmlFor="branch">Branch/Department</label>
                            <input
                                id="branch"
                                type="text"
                                placeholder="e.g., Computer Science, Mechanical"
                                value={branch}
                                onChange={(e) => setBranch(e.target.value)}
                                required
                            />
                        </div>
                        
                        <div className="form-group">
                            <label htmlFor="year">Year of Study</label>
                            <input
                                id="year"
                                type="text"
                                placeholder="e.g., 1st Year, 2nd Year"
                                value={yearOfStudy}
                                onChange={(e) => setYearOfStudy(e.target.value)}
                                required
                            />
                        </div>
                        
                        <button type="submit">Register as Campus Ambassador</button>
                    </form>
                    
                    {message && (
                        <div className={`message ${message.includes('successful') ? 'success' : 'error'}`}>
                            {message}
                        </div>
                    )}
                </div>
            );
        }

        // Admin Login Component
        function AdminLogin() {
            const [userid, setUserid] = useState('admin');
            const [password, setPassword] = useState('admin123');
            const [message, setMessage] = useState('');
            const [isLoggedIn, setIsLoggedIn] = useState(false);
            const [registrations, setRegistrations] = useState([]);

            const handleSubmit = async (e) => {
                e.preventDefault();
                setMessage('');
                
                try {
                    const formData = new FormData();
                    formData.append('userid', userid);
                    formData.append('password', password);

                    const res = await fetch('http://localhost:8000/admin/login', {
                        method: 'POST',
                        body: formData,
                    });

                    if (res.ok) {
                        setMessage('Login successful');
                        setIsLoggedIn(true);
                        fetchRegistrations();
                    } else {
                        const data = await res.json();
                        setMessage(data.detail || 'Login failed');
                    }
                } catch (error) {
                    setMessage('Network error: ' + error.message);
                }
            };

            const fetchRegistrations = async () => {
                try {
                    const res = await fetch('http://localhost:8000/admin/registrations');
                    if (res.ok) {
                        const data = await res.json();
                        setRegistrations(data);
                    }
                } catch (error) {
                    console.error('Error fetching registrations:', error);
                }
            };

            const logout = () => {
                setIsLoggedIn(false);
                setRegistrations([]);
                setMessage('');
            };

            if (isLoggedIn) {
                return (
                    <div>
                        <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                            <h2>Admin Dashboard</h2>
                            <button onClick={logout} style={{background: '#dc3545'}}>Logout</button>
                        </div>
                        
                        <h3>Campus Ambassador Registrations ({registrations.length})</h3>
                        
                        {registrations.length === 0 ? (
                            <p>No registrations yet.</p>
                        ) : (
                            <div style={{overflowX: 'auto'}}>
                                <table style={{width: '100%', borderCollapse: 'collapse', marginTop: '20px'}}>
                                    <thead>
                                        <tr style={{backgroundColor: '#f8f9fa'}}>
                                            <th style={{padding: '12px', border: '1px solid #ddd', textAlign: 'left'}}>ID</th>
                                            <th style={{padding: '12px', border: '1px solid #ddd', textAlign: 'left'}}>Name</th>
                                            <th style={{padding: '12px', border: '1px solid #ddd', textAlign: 'left'}}>Email</th>
                                            <th style={{padding: '12px', border: '1px solid #ddd', textAlign: 'left'}}>Mobile</th>
                                            <th style={{padding: '12px', border: '1px solid #ddd', textAlign: 'left'}}>College</th>
                                            <th style={{padding: '12px', border: '1px solid #ddd', textAlign: 'left'}}>Branch</th>
                                            <th style={{padding: '12px', border: '1px solid #ddd', textAlign: 'left'}}>Year</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {registrations.map((reg) => (
                                            <tr key={reg.id}>
                                                <td style={{padding: '12px', border: '1px solid #ddd'}}>{reg.id}</td>
                                                <td style={{padding: '12px', border: '1px solid #ddd'}}>{reg.name}</td>
                                                <td style={{padding: '12px', border: '1px solid #ddd'}}>{reg.email}</td>
                                                <td style={{padding: '12px', border: '1px solid #ddd'}}>{reg.mobile}</td>
                                                <td style={{padding: '12px', border: '1px solid #ddd'}}>{reg.college}</td>
                                                <td style={{padding: '12px', border: '1px solid #ddd'}}>{reg.branch}</td>
                                                <td style={{padding: '12px', border: '1px solid #ddd'}}>{reg.year_of_study}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </div>
                );
            }

            return (
                <div>
                    <h2>Admin Login</h2>
                    <form onSubmit={handleSubmit}>
                        <div className="form-group">
                            <label htmlFor="userid">User ID</label>
                            <input
                                id="userid"
                                type="text"
                                placeholder="Enter admin user ID"
                                value={userid}
                                onChange={(e) => setUserid(e.target.value)}
                                required
                            />
                        </div>
                        
                        <div className="form-group">
                            <label htmlFor="password">Password</label>
                            <input
                                id="password"
                                type="password"
                                placeholder="Enter admin password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                required
                            />
                        </div>
                        
                        <button type="submit">Login</button>
                    </form>
                    
                    {message && (
                        <div className={`message ${message.includes('successful') ? 'success' : 'error'}`}>
                            {message}
                        </div>
                    )}
                </div>
            );
        }

        // Render components
        ReactDOM.render(<CARegister />, document.getElementById('register-app'));
        ReactDOM.render(<AdminLogin />, document.getElementById('admin-app'));

        // Tab switching function
        function showTab(tabName) {
            // Hide all tabs
            document.getElementById('register-tab').classList.add('hidden');
            document.getElementById('admin-tab').classList.add('hidden');
            
            // Remove active class from all buttons
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            
            // Add active class to clicked button
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
