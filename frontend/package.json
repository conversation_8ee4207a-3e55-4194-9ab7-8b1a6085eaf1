{"name": "campus-ambassador-frontend", "version": "1.0.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build"}, "dependencies": {"http-proxy-middleware": "^2.0.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^5.3.4", "react-scripts": "5.0.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}