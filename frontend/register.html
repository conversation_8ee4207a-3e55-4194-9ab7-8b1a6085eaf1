<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Register - Campus Ambassador Program</title>
<style>
  *, *::before, *::after {
    box-sizing: border-box;
  }
  body {
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
  }
  .container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 3rem;
    width: 100%;
    max-width: 600px;
  }
  .header {
    text-align: center;
    margin-bottom: 2rem;
  }
  .header h1 {
    color: #1e293b;
    font-size: 2rem;
    margin: 0 0 0.5rem;
  }
  .header p {
    color: #64748b;
    margin: 0;
  }
  .form-group {
    margin-bottom: 1.5rem;
  }
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
  }
  input, textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.3s;
  }
  input:focus, textarea:focus {
    outline: none;
    border-color: #3b82f6;
  }
  .btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    transition: transform 0.2s;
  }
  .btn-primary:hover {
    transform: translateY(-2px);
  }
  .btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
  .message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 500;
  }
  .success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
  }
  .error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
  }
  .back-link {
    display: inline-block;
    margin-bottom: 1rem;
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
  }
  .back-link:hover {
    text-decoration: underline;
  }
  .loading {
    display: none;
    text-align: center;
    color: #64748b;
  }
  @media (max-width: 600px) {
    .container {
      padding: 2rem 1.5rem;
    }
    .form-row {
      grid-template-columns: 1fr;
    }
    .header h1 {
      font-size: 1.5rem;
    }
  }
</style>
</head>
<body>

<div class="container">
  <a href="landing.html" class="back-link">← Back to Home</a>
  
  <div class="header">
    <h1>Join as Campus Ambassador</h1>
    <p>Fill out the form below to start your journey with us</p>
  </div>

  <div id="message"></div>
  <div id="loading" class="loading">Submitting your application...</div>

  <form id="registrationForm">
    <div class="form-row">
      <div class="form-group">
        <label for="name">Full Name *</label>
        <input type="text" id="name" name="name" required>
      </div>
      <div class="form-group">
        <label for="email">Email Address *</label>
        <input type="email" id="email" name="email" required>
      </div>
    </div>

    <div class="form-group">
      <label for="mobile">Mobile Number *</label>
      <input type="tel" id="mobile" name="mobile" required>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="college">College/University *</label>
        <input type="text" id="college" name="college" required>
      </div>
      <div class="form-group">
        <label for="branch">Branch/Department *</label>
        <input type="text" id="branch" name="branch" required>
      </div>
    </div>

    <div class="form-group">
      <label for="year_of_study">Year of Study *</label>
      <input type="text" id="year_of_study" name="year_of_study" placeholder="e.g., 2nd Year, Final Year" required>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="linkedin">LinkedIn Profile (Optional)</label>
        <input type="url" id="linkedin" name="linkedin" placeholder="https://linkedin.com/in/yourprofile">
      </div>
      <div class="form-group">
        <label for="instagram">Instagram Handle (Optional)</label>
        <input type="text" id="instagram" name="instagram" placeholder="@yourusername">
      </div>
    </div>

    <button type="submit" class="btn-primary" id="submitBtn">
      Register as Campus Ambassador
    </button>
  </form>
</div>

<script>
document.getElementById('registrationForm').addEventListener('submit', async (e) => {
  e.preventDefault();
  
  const submitBtn = document.getElementById('submitBtn');
  const loading = document.getElementById('loading');
  const messageDiv = document.getElementById('message');
  
  // Get form data
  const formData = {
    name: document.getElementById('name').value.trim(),
    email: document.getElementById('email').value.trim(),
    mobile: document.getElementById('mobile').value.trim(),
    college: document.getElementById('college').value.trim(),
    branch: document.getElementById('branch').value.trim(),
    year_of_study: document.getElementById('year_of_study').value.trim(),
    linkedin: document.getElementById('linkedin').value.trim() || null,
    instagram: document.getElementById('instagram').value.trim() || null,
    referral_code: localStorage.getItem('referralCode') || null
  };

  // Show loading state
  submitBtn.disabled = true;
  loading.style.display = 'block';
  messageDiv.innerHTML = '';

  // Debug logging
  console.log('Submitting form data:', formData);

  try {
    const response = await fetch('http://localhost:8000/ca/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData)
    });

    console.log('Response status:', response.status);

    const data = await response.json();

    if (response.ok) {
      // Success - redirect to success page with CA data
      localStorage.setItem('caData', JSON.stringify(data));
      window.location.href = 'success.html';
    } else {
      // Error
      messageDiv.innerHTML = `<div class="message error">${data.detail || 'Registration failed. Please try again.'}</div>`;
    }
  } catch (error) {
    messageDiv.innerHTML = `<div class="message error">Network error. Please check your connection and try again.</div>`;
  } finally {
    submitBtn.disabled = false;
    loading.style.display = 'none';
  }
});

// Auto-format phone number
document.getElementById('mobile').addEventListener('input', (e) => {
  let value = e.target.value.replace(/\D/g, '');
  if (value.length > 10) value = value.slice(0, 10);
  e.target.value = value;
});

// Auto-format Instagram handle
document.getElementById('instagram').addEventListener('input', (e) => {
  let value = e.target.value;
  if (value && !value.startsWith('@')) {
    e.target.value = '@' + value;
  }
});
</script>

</body>
</html>
