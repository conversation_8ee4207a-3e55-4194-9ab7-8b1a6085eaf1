{"ast": null, "code": "'use client';\n\nvar _jsxFileName = \"/home/<USER>/Downloads/campus_ambassador_full/frontend/src/components/CALandingPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CALandingPage({\n  onAdminLogin\n}) {\n  _s();\n  const [userid, setUserid] = useState('');\n  const [password, setPassword] = useState('');\n  const [loginError, setLoginError] = useState('');\n  const handleAdminLogin = async e => {\n    e.preventDefault();\n    try {\n      const res = await fetch('/admin/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          userid,\n          password\n        })\n      });\n      if (res.ok) {\n        setLoginError('');\n        onAdminLogin();\n      } else {\n        const data = await res.json();\n        setLoginError(data.detail || 'Login failed');\n      }\n    } catch (err) {\n      setLoginError('Login failed');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Campus Ambassador Program\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Welcome to the Campus Ambassador Program. Join us to promote and earn rewards!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => window.location.href = '/dashboard',\n      children: \"Apply Now\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Admin Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleAdminLogin,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"User ID\",\n        value: userid,\n        onChange: e => setUserid(e.target.value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"password\",\n        placeholder: \"Password\",\n        value: password,\n        onChange: e => setPassword(e.target.value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this), loginError && /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        color: 'red'\n      },\n      children: loginError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 28\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 9\n  }, this);\n}\n_s(CALandingPage, \"fKEreJCiAl/YGUYT937HUz3f23g=\");\n_c = CALandingPage;\nvar _c;\n$RefreshReg$(_c, \"CALandingPage\");", "map": {"version": 3, "names": ["_jsxFileName", "_s", "$RefreshSig$", "React", "useState", "jsxDEV", "_jsxDEV", "CALandingPage", "onAdminLogin", "userid", "setUserid", "password", "setPassword", "loginError", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleAdminLogin", "e", "preventDefault", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "data", "json", "detail", "err", "children", "fileName", "lineNumber", "columnNumber", "onClick", "window", "location", "href", "onSubmit", "type", "placeholder", "value", "onChange", "target", "required", "style", "color", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Downloads/campus_ambassador_full/frontend/src/components/CALandingPage.jsx"], "sourcesContent": ["'use client'\nimport React, { useState } from 'react';\n\nexport default function CALandingPage({ onAdminLogin }) {\n    const [userid, setUserid] = useState('');\n    const [password, setPassword] = useState('');\n    const [loginError, setLoginError] = useState('');\n\n    const handleAdminLogin = async (e) => {\n        e.preventDefault();\n        try {\n            const res = await fetch('/admin/login', {\n                method: 'POST',\n                headers: { 'Content-Type': 'application/json' },\n                body: JSON.stringify({ userid, password }),\n            });\n            if (res.ok) {\n                setLoginError('');\n                onAdminLogin();\n            } else {\n                const data = await res.json();\n                setLoginError(data.detail || 'Login failed');\n            }\n        } catch (err) {\n            setLoginError('Login failed');\n        }\n    };\n\n    return (\n        <div>\n            <h1>Campus Ambassador Program</h1>\n            <p>Welcome to the Campus Ambassador Program. Join us to promote and earn rewards!</p>\n\n            <button onClick={() => window.location.href = '/dashboard'}>Apply Now</button>\n\n            <h2>Admin Login</h2>\n            <form onSubmit={handleAdminLogin}>\n                <input\n                    type=\"text\"\n                    placeholder=\"User ID\"\n                    value={userid}\n                    onChange={(e) => setUserid(e.target.value)}\n                    required\n                />\n                <input\n                    type=\"password\"\n                    placeholder=\"Password\"\n                    value={password}\n                    onChange={(e) => setPassword(e.target.value)}\n                    required\n                />\n                <button type=\"submit\">Login</button>\n            </form>\n            {loginError && <p style={{ color: 'red' }}>{loginError}</p>}\n        </div>\n    );\n}\n"], "mappings": "AAAA,YAAY;;AAAA,IAAAA,YAAA;EAAAC,EAAA,GAAAC,YAAA;AACZ,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,eAAe,SAASC,aAAaA,CAAC;EAAEC;AAAa,CAAC,EAAE;EAAAP,EAAA;EACpD,MAAM,CAACQ,MAAM,EAAEC,SAAS,CAAC,GAAGN,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMW,gBAAgB,GAAG,MAAOC,CAAC,IAAK;IAClCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACA,MAAMC,GAAG,GAAG,MAAMC,KAAK,CAAC,cAAc,EAAE;QACpCC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEf,MAAM;UAAEE;QAAS,CAAC;MAC7C,CAAC,CAAC;MACF,IAAIO,GAAG,CAACO,EAAE,EAAE;QACRX,aAAa,CAAC,EAAE,CAAC;QACjBN,YAAY,CAAC,CAAC;MAClB,CAAC,MAAM;QACH,MAAMkB,IAAI,GAAG,MAAMR,GAAG,CAACS,IAAI,CAAC,CAAC;QAC7Bb,aAAa,CAACY,IAAI,CAACE,MAAM,IAAI,cAAc,CAAC;MAChD;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVf,aAAa,CAAC,cAAc,CAAC;IACjC;EACJ,CAAC;EAED,oBACIR,OAAA;IAAAwB,QAAA,gBACIxB,OAAA;MAAAwB,QAAA,EAAI;IAAyB;MAAAC,QAAA,EAAA/B,YAAA;MAAAgC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAClC3B,OAAA;MAAAwB,QAAA,EAAG;IAA8E;MAAAC,QAAA,EAAA/B,YAAA;MAAAgC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAErF3B,OAAA;MAAQ4B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAa;MAAAP,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAA/B,YAAA;MAAAgC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAE9E3B,OAAA;MAAAwB,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAA/B,YAAA;MAAAgC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpB3B,OAAA;MAAMgC,QAAQ,EAAEvB,gBAAiB;MAAAe,QAAA,gBAC7BxB,OAAA;QACIiC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,SAAS;QACrBC,KAAK,EAAEhC,MAAO;QACdiC,QAAQ,EAAG1B,CAAC,IAAKN,SAAS,CAACM,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE;QAC3CG,QAAQ;MAAA;QAAAb,QAAA,EAAA/B,YAAA;QAAAgC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACF3B,OAAA;QACIiC,IAAI,EAAC,UAAU;QACfC,WAAW,EAAC,UAAU;QACtBC,KAAK,EAAE9B,QAAS;QAChB+B,QAAQ,EAAG1B,CAAC,IAAKJ,WAAW,CAACI,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE;QAC7CG,QAAQ;MAAA;QAAAb,QAAA,EAAA/B,YAAA;QAAAgC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACF3B,OAAA;QAAQiC,IAAI,EAAC,QAAQ;QAAAT,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAA/B,YAAA;QAAAgC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAF,QAAA,EAAA/B,YAAA;MAAAgC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,EACNpB,UAAU,iBAAIP,OAAA;MAAGuC,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAM,CAAE;MAAAhB,QAAA,EAAEjB;IAAU;MAAAkB,QAAA,EAAA/B,YAAA;MAAAgC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAF,QAAA,EAAA/B,YAAA;IAAAgC,UAAA;IAAAC,YAAA;EAAA,OAC1D,CAAC;AAEd;AAAChC,EAAA,CArDuBM,aAAa;AAAAwC,EAAA,GAAbxC,aAAa;AAAA,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}