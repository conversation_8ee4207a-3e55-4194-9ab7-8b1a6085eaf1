{"ast": null, "code": "'use client';\n\nvar _jsxFileName = \"/home/<USER>/Downloads/campus_ambassador_full/frontend/src/components/CADashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CADashboard() {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    mobile: '',\n    college: '',\n    branch: '',\n    year_of_study: ''\n  });\n  const [message, setMessage] = useState('');\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setMessage('');\n    try {\n      const res = await fetch('/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      });\n      if (res.ok) {\n        setMessage('Registration successful!');\n        setFormData({\n          name: '',\n          email: '',\n          mobile: '',\n          college: '',\n          branch: '',\n          year_of_study: ''\n        });\n      } else {\n        const data = await res.json();\n        setMessage(data.detail || 'Registration failed');\n      }\n    } catch (err) {\n      setMessage('Registration failed');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"CA Dashboard - Registration Form\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        name: \"name\",\n        placeholder: \"Name\",\n        value: formData.name,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"email\",\n        name: \"email\",\n        placeholder: \"Email\",\n        value: formData.email,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"tel\",\n        name: \"mobile\",\n        placeholder: \"Mobile Number\",\n        value: formData.mobile,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        name: \"college\",\n        placeholder: \"College\",\n        value: formData.college,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        name: \"branch\",\n        placeholder: \"Branch\",\n        value: formData.branch,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        name: \"year_of_study\",\n        placeholder: \"Year of Study\",\n        value: formData.year_of_study,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Submit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 13\n    }, this), message && /*#__PURE__*/_jsxDEV(\"p\", {\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 25\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 9\n  }, this);\n}\n_s(CADashboard, \"7DrfgMRvZM4Xh8eeNBxBRKjfGXk=\");\n_c = CADashboard;\nvar _c;\n$RefreshReg$(_c, \"CADashboard\");", "map": {"version": 3, "names": ["_jsxFileName", "_s", "$RefreshSig$", "React", "useState", "jsxDEV", "_jsxDEV", "CADashboard", "formData", "setFormData", "name", "email", "mobile", "college", "branch", "year_of_study", "message", "setMessage", "handleChange", "e", "target", "value", "handleSubmit", "preventDefault", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "data", "json", "detail", "err", "children", "fileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "onChange", "required", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Downloads/campus_ambassador_full/frontend/src/components/CADashboard.jsx"], "sourcesContent": ["'use client'\nimport React, { useState } from 'react';\n\nexport default function CADashboard() {\n    const [formData, setFormData] = useState({\n        name: '',\n        email: '',\n        mobile: '',\n        college: '',\n        branch: '',\n        year_of_study: ''\n    });\n    const [message, setMessage] = useState('');\n\n    const handleChange = (e) => {\n        setFormData({...formData, [e.target.name]: e.target.value});\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setMessage('');\n        try {\n            const res = await fetch('/register', {\n                method: 'POST',\n                headers: {'Content-Type': 'application/json'},\n                body: JSON.stringify(formData)\n            });\n            if (res.ok) {\n                setMessage('Registration successful!');\n                setFormData({\n                    name: '',\n                    email: '',\n                    mobile: '',\n                    college: '',\n                    branch: '',\n                    year_of_study: ''\n                });\n            } else {\n                const data = await res.json();\n                setMessage(data.detail || 'Registration failed');\n            }\n        } catch (err) {\n            setMessage('Registration failed');\n        }\n    };\n\n    return (\n        <div>\n            <h1>CA Dashboard - Registration Form</h1>\n            <form onSubmit={handleSubmit}>\n                <input type=\"text\" name=\"name\" placeholder=\"Name\" value={formData.name} onChange={handleChange} required />\n                <input type=\"email\" name=\"email\" placeholder=\"Email\" value={formData.email} onChange={handleChange} required />\n                <input type=\"tel\" name=\"mobile\" placeholder=\"Mobile Number\" value={formData.mobile} onChange={handleChange} required />\n                <input type=\"text\" name=\"college\" placeholder=\"College\" value={formData.college} onChange={handleChange} required />\n                <input type=\"text\" name=\"branch\" placeholder=\"Branch\" value={formData.branch} onChange={handleChange} required />\n                <input type=\"text\" name=\"year_of_study\" placeholder=\"Year of Study\" value={formData.year_of_study} onChange={handleChange} required />\n                <button type=\"submit\">Submit</button>\n            </form>\n            {message && <p>{message}</p>}\n        </div>\n    );\n}\n"], "mappings": "AAAA,YAAY;;AAAA,IAAAA,YAAA;EAAAC,EAAA,GAAAC,YAAA;AACZ,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAN,EAAA;EAClC,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGL,QAAQ,CAAC;IACrCM,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,EAAE;IACVC,aAAa,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMc,YAAY,GAAIC,CAAC,IAAK;IACxBV,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAE,CAACW,CAAC,CAACC,MAAM,CAACV,IAAI,GAAGS,CAAC,CAACC,MAAM,CAACC;IAAK,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOH,CAAC,IAAK;IAC9BA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBN,UAAU,CAAC,EAAE,CAAC;IACd,IAAI;MACA,MAAMO,GAAG,GAAG,MAAMC,KAAK,CAAC,WAAW,EAAE;QACjCC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAC,cAAc,EAAE;QAAkB,CAAC;QAC7CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACtB,QAAQ;MACjC,CAAC,CAAC;MACF,IAAIgB,GAAG,CAACO,EAAE,EAAE;QACRd,UAAU,CAAC,0BAA0B,CAAC;QACtCR,WAAW,CAAC;UACRC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE,EAAE;UACVC,aAAa,EAAE;QACnB,CAAC,CAAC;MACN,CAAC,MAAM;QACH,MAAMiB,IAAI,GAAG,MAAMR,GAAG,CAACS,IAAI,CAAC,CAAC;QAC7BhB,UAAU,CAACe,IAAI,CAACE,MAAM,IAAI,qBAAqB,CAAC;MACpD;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVlB,UAAU,CAAC,qBAAqB,CAAC;IACrC;EACJ,CAAC;EAED,oBACIX,OAAA;IAAA8B,QAAA,gBACI9B,OAAA;MAAA8B,QAAA,EAAI;IAAgC;MAAAC,QAAA,EAAArC,YAAA;MAAAsC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzCjC,OAAA;MAAMkC,QAAQ,EAAElB,YAAa;MAAAc,QAAA,gBACzB9B,OAAA;QAAOmC,IAAI,EAAC,MAAM;QAAC/B,IAAI,EAAC,MAAM;QAACgC,WAAW,EAAC,MAAM;QAACrB,KAAK,EAAEb,QAAQ,CAACE,IAAK;QAACiC,QAAQ,EAAEzB,YAAa;QAAC0B,QAAQ;MAAA;QAAAP,QAAA,EAAArC,YAAA;QAAAsC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3GjC,OAAA;QAAOmC,IAAI,EAAC,OAAO;QAAC/B,IAAI,EAAC,OAAO;QAACgC,WAAW,EAAC,OAAO;QAACrB,KAAK,EAAEb,QAAQ,CAACG,KAAM;QAACgC,QAAQ,EAAEzB,YAAa;QAAC0B,QAAQ;MAAA;QAAAP,QAAA,EAAArC,YAAA;QAAAsC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/GjC,OAAA;QAAOmC,IAAI,EAAC,KAAK;QAAC/B,IAAI,EAAC,QAAQ;QAACgC,WAAW,EAAC,eAAe;QAACrB,KAAK,EAAEb,QAAQ,CAACI,MAAO;QAAC+B,QAAQ,EAAEzB,YAAa;QAAC0B,QAAQ;MAAA;QAAAP,QAAA,EAAArC,YAAA;QAAAsC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvHjC,OAAA;QAAOmC,IAAI,EAAC,MAAM;QAAC/B,IAAI,EAAC,SAAS;QAACgC,WAAW,EAAC,SAAS;QAACrB,KAAK,EAAEb,QAAQ,CAACK,OAAQ;QAAC8B,QAAQ,EAAEzB,YAAa;QAAC0B,QAAQ;MAAA;QAAAP,QAAA,EAAArC,YAAA;QAAAsC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpHjC,OAAA;QAAOmC,IAAI,EAAC,MAAM;QAAC/B,IAAI,EAAC,QAAQ;QAACgC,WAAW,EAAC,QAAQ;QAACrB,KAAK,EAAEb,QAAQ,CAACM,MAAO;QAAC6B,QAAQ,EAAEzB,YAAa;QAAC0B,QAAQ;MAAA;QAAAP,QAAA,EAAArC,YAAA;QAAAsC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjHjC,OAAA;QAAOmC,IAAI,EAAC,MAAM;QAAC/B,IAAI,EAAC,eAAe;QAACgC,WAAW,EAAC,eAAe;QAACrB,KAAK,EAAEb,QAAQ,CAACO,aAAc;QAAC4B,QAAQ,EAAEzB,YAAa;QAAC0B,QAAQ;MAAA;QAAAP,QAAA,EAAArC,YAAA;QAAAsC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtIjC,OAAA;QAAQmC,IAAI,EAAC,QAAQ;QAAAL,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAArC,YAAA;QAAAsC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAF,QAAA,EAAArC,YAAA;MAAAsC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,EACNvB,OAAO,iBAAIV,OAAA;MAAA8B,QAAA,EAAIpB;IAAO;MAAAqB,QAAA,EAAArC,YAAA;MAAAsC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAF,QAAA,EAAArC,YAAA;IAAAsC,UAAA;IAAAC,YAAA;EAAA,OAC3B,CAAC;AAEd;AAACtC,EAAA,CA1DuBM,WAAW;AAAAsC,EAAA,GAAXtC,WAAW;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}