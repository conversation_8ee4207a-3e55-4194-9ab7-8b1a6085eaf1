{"ast": null, "code": "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\nfunction toObject(val) {\n  if (val === null || val === undefined) {\n    throw new TypeError('Object.assign cannot be called with null or undefined');\n  }\n  return Object(val);\n}\nfunction shouldUseNative() {\n  try {\n    if (!Object.assign) {\n      return false;\n    }\n\n    // Detect buggy property enumeration order in older V8 versions.\n\n    // https://bugs.chromium.org/p/v8/issues/detail?id=4118\n    var test1 = new String('abc'); // eslint-disable-line no-new-wrappers\n    test1[5] = 'de';\n    if (Object.getOwnPropertyNames(test1)[0] === '5') {\n      return false;\n    }\n\n    // https://bugs.chromium.org/p/v8/issues/detail?id=3056\n    var test2 = {};\n    for (var i = 0; i < 10; i++) {\n      test2['_' + String.fromCharCode(i)] = i;\n    }\n    var order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n      return test2[n];\n    });\n    if (order2.join('') !== '0123456789') {\n      return false;\n    }\n\n    // https://bugs.chromium.org/p/v8/issues/detail?id=3056\n    var test3 = {};\n    'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n      test3[letter] = letter;\n    });\n    if (Object.keys(Object.assign({}, test3)).join('') !== 'abcdefghijklmnopqrst') {\n      return false;\n    }\n    return true;\n  } catch (err) {\n    // We don't expect any of the above to throw, but better to be safe.\n    return false;\n  }\n}\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n  var from;\n  var to = toObject(target);\n  var symbols;\n  for (var s = 1; s < arguments.length; s++) {\n    from = Object(arguments[s]);\n    for (var key in from) {\n      if (hasOwnProperty.call(from, key)) {\n        to[key] = from[key];\n      }\n    }\n    if (getOwnPropertySymbols) {\n      symbols = getOwnPropertySymbols(from);\n      for (var i = 0; i < symbols.length; i++) {\n        if (propIsEnumerable.call(from, symbols[i])) {\n          to[symbols[i]] = from[symbols[i]];\n        }\n      }\n    }\n  }\n  return to;\n};", "map": {"version": 3, "names": ["getOwnPropertySymbols", "Object", "hasOwnProperty", "prototype", "propIsEnumerable", "propertyIsEnumerable", "toObject", "val", "undefined", "TypeError", "shouldUseNative", "assign", "test1", "String", "getOwnPropertyNames", "test2", "i", "fromCharCode", "order2", "map", "n", "join", "test3", "split", "for<PERSON>ach", "letter", "keys", "err", "module", "exports", "target", "source", "from", "to", "symbols", "s", "arguments", "length", "key", "call"], "sources": ["/home/<USER>/Downloads/campus_ambassador_full/frontend/node_modules/object-assign/index.js"], "sourcesContent": ["/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AACZ;AACA,IAAIA,qBAAqB,GAAGC,MAAM,CAACD,qBAAqB;AACxD,IAAIE,cAAc,GAAGD,MAAM,CAACE,SAAS,CAACD,cAAc;AACpD,IAAIE,gBAAgB,GAAGH,MAAM,CAACE,SAAS,CAACE,oBAAoB;AAE5D,SAASC,QAAQA,CAACC,GAAG,EAAE;EACtB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,uDAAuD,CAAC;EAC7E;EAEA,OAAOR,MAAM,CAACM,GAAG,CAAC;AACnB;AAEA,SAASG,eAAeA,CAAA,EAAG;EAC1B,IAAI;IACH,IAAI,CAACT,MAAM,CAACU,MAAM,EAAE;MACnB,OAAO,KAAK;IACb;;IAEA;;IAEA;IACA,IAAIC,KAAK,GAAG,IAAIC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAE;IAChCD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;IACf,IAAIX,MAAM,CAACa,mBAAmB,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACjD,OAAO,KAAK;IACb;;IAEA;IACA,IAAIG,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC5BD,KAAK,CAAC,GAAG,GAAGF,MAAM,CAACI,YAAY,CAACD,CAAC,CAAC,CAAC,GAAGA,CAAC;IACxC;IACA,IAAIE,MAAM,GAAGjB,MAAM,CAACa,mBAAmB,CAACC,KAAK,CAAC,CAACI,GAAG,CAAC,UAAUC,CAAC,EAAE;MAC/D,OAAOL,KAAK,CAACK,CAAC,CAAC;IAChB,CAAC,CAAC;IACF,IAAIF,MAAM,CAACG,IAAI,CAAC,EAAE,CAAC,KAAK,YAAY,EAAE;MACrC,OAAO,KAAK;IACb;;IAEA;IACA,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,sBAAsB,CAACC,KAAK,CAAC,EAAE,CAAC,CAACC,OAAO,CAAC,UAAUC,MAAM,EAAE;MAC1DH,KAAK,CAACG,MAAM,CAAC,GAAGA,MAAM;IACvB,CAAC,CAAC;IACF,IAAIxB,MAAM,CAACyB,IAAI,CAACzB,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEW,KAAK,CAAC,CAAC,CAACD,IAAI,CAAC,EAAE,CAAC,KAChD,sBAAsB,EAAE;MACzB,OAAO,KAAK;IACb;IAEA,OAAO,IAAI;EACZ,CAAC,CAAC,OAAOM,GAAG,EAAE;IACb;IACA,OAAO,KAAK;EACb;AACD;AAEAC,MAAM,CAACC,OAAO,GAAGnB,eAAe,CAAC,CAAC,GAAGT,MAAM,CAACU,MAAM,GAAG,UAAUmB,MAAM,EAAEC,MAAM,EAAE;EAC9E,IAAIC,IAAI;EACR,IAAIC,EAAE,GAAG3B,QAAQ,CAACwB,MAAM,CAAC;EACzB,IAAII,OAAO;EAEX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAC1CH,IAAI,GAAG/B,MAAM,CAACmC,SAAS,CAACD,CAAC,CAAC,CAAC;IAE3B,KAAK,IAAIG,GAAG,IAAIN,IAAI,EAAE;MACrB,IAAI9B,cAAc,CAACqC,IAAI,CAACP,IAAI,EAAEM,GAAG,CAAC,EAAE;QACnCL,EAAE,CAACK,GAAG,CAAC,GAAGN,IAAI,CAACM,GAAG,CAAC;MACpB;IACD;IAEA,IAAItC,qBAAqB,EAAE;MAC1BkC,OAAO,GAAGlC,qBAAqB,CAACgC,IAAI,CAAC;MACrC,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,OAAO,CAACG,MAAM,EAAErB,CAAC,EAAE,EAAE;QACxC,IAAIZ,gBAAgB,CAACmC,IAAI,CAACP,IAAI,EAAEE,OAAO,CAAClB,CAAC,CAAC,CAAC,EAAE;UAC5CiB,EAAE,CAACC,OAAO,CAAClB,CAAC,CAAC,CAAC,GAAGgB,IAAI,CAACE,OAAO,CAAClB,CAAC,CAAC,CAAC;QAClC;MACD;IACD;EACD;EAEA,OAAOiB,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}