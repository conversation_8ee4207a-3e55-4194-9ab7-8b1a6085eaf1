{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Downloads/campus_ambassador_full/frontend/src/App.js\";\nimport React from 'react';\nimport { BrowserRouter, Switch, Route } from 'react-router-dom';\nimport CALandingPage from './components/CALandingPage';\nimport CADashboard from './components/CADashboard';\nimport CAAdminPanel from './components/CAAdminPanel';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(Switch, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        exact: true,\n        path: \"/\",\n        component: CALandingPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard\",\n        component: CADashboard\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/admin\",\n        component: CAAdminPanel\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Switch", "Route", "CALandingPage", "CADashboard", "CAAdminPanel", "jsxDEV", "_jsxDEV", "App", "children", "exact", "path", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Downloads/campus_ambassador_full/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Switch, Route } from 'react-router-dom';\nimport CALandingPage from './components/CALandingPage';\nimport CADashboard from './components/CADashboard';\nimport CAAdminPanel from './components/CAAdminPanel';\n\nfunction App() {\n  return (\n    <BrowserRouter>\n      <Switch>\n        <Route exact path=\"/\" component={CALandingPage} />\n        <Route path=\"/dashboard\" component={CADashboard} />\n        <Route path=\"/admin\" component={CAAdminPanel} />\n      </Switch>\n    </BrowserRouter>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACP,aAAa;IAAAS,QAAA,eACZF,OAAA,CAACN,MAAM;MAAAQ,QAAA,gBACLF,OAAA,CAACL,KAAK;QAACQ,KAAK;QAACC,IAAI,EAAC,GAAG;QAACC,SAAS,EAAET;MAAc;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDT,OAAA,CAACL,KAAK;QAACS,IAAI,EAAC,YAAY;QAACC,SAAS,EAAER;MAAY;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnDT,OAAA,CAACL,KAAK;QAACS,IAAI,EAAC,QAAQ;QAACC,SAAS,EAAEP;MAAa;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACC,EAAA,GAVQT,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}