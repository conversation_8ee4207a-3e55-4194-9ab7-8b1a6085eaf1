{"ast": null, "code": "'use client';\n\nvar _jsxFileName = \"/home/<USER>/Downloads/campus_ambassador_full/frontend/src/components/CAAdminPanel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function CAAdminPanel() {\n  _s();\n  const [cas, setCas] = useState([]);\n  const [referrals, setReferrals] = useState([]);\n  const [emailSubject, setEmailSubject] = useState('');\n  const [emailBody, setEmailBody] = useState('');\n  const [message, setMessage] = useState('');\n  useEffect(() => {\n    fetch('/admin/cas').then(res => res.json()).then(data => setCas(data));\n    fetch('/admin/referrals').then(res => res.json()).then(data => setReferrals(data));\n  }, []);\n  const handleApprove = (email, approve) => {\n    fetch('/admin/approve-ca', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        email,\n        approve\n      })\n    }).then(res => res.json()).then(data => {\n      setMessage(data.message);\n      // Refresh CA list\n      fetch('/admin/cas').then(res => res.json()).then(data => setCas(data));\n    });\n  };\n  const handleSendEmail = e => {\n    e.preventDefault();\n    const recipients = cas.map(ca => ca.email);\n    fetch('/admin/send-email', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        subject: emailSubject,\n        body: emailBody,\n        recipients\n      })\n    }).then(res => res.json()).then(data => setMessage(data.message));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Admin Panel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this), message && /*#__PURE__*/_jsxDEV(\"p\", {\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 25\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Campus Ambassadors\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: cas.map((ca, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [ca.name, \" (\", ca.email, \") - \", ca.status, ca.status === 'pending' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleApprove(ca.email, true),\n              children: \"Approve\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleApprove(ca.email, false),\n              children: \"Disqualify\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true)]\n        }, idx, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Referral Logs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: referrals.map((ref, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [ref.referrer_email, \" referred \", ref.referred_email, \" on \", new Date(ref.timestamp).toLocaleString()]\n        }, idx, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Send Bulk Email\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSendEmail,\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Subject\",\n          value: emailSubject,\n          onChange: e => setEmailSubject(e.target.value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          placeholder: \"Email body\",\n          value: emailBody,\n          onChange: e => setEmailBody(e.target.value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          children: \"Send Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 9\n  }, this);\n}\n_s(CAAdminPanel, \"wblmWly0ZH0DAHay0cuSBHholjI=\");\n_c = CAAdminPanel;\nvar _c;\n$RefreshReg$(_c, \"CAAdminPanel\");", "map": {"version": 3, "names": ["_jsxFileName", "_s", "$RefreshSig$", "React", "useEffect", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CAAdminPanel", "cas", "setCas", "referrals", "setReferrals", "emailSubject", "setEmailSubject", "emailBody", "setEmailBody", "message", "setMessage", "fetch", "then", "res", "json", "data", "handleApprove", "email", "approve", "method", "headers", "body", "JSON", "stringify", "handleSendEmail", "e", "preventDefault", "recipients", "map", "ca", "subject", "children", "fileName", "lineNumber", "columnNumber", "idx", "name", "status", "onClick", "ref", "referrer_email", "referred_email", "Date", "timestamp", "toLocaleString", "onSubmit", "type", "placeholder", "value", "onChange", "target", "required", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Downloads/campus_ambassador_full/frontend/src/components/CAAdminPanel.jsx"], "sourcesContent": ["'use client'\nimport React, { useEffect, useState } from 'react';\n\nexport default function CAAdminPanel() {\n    const [cas, setCas] = useState([]);\n    const [referrals, setReferrals] = useState([]);\n    const [emailSubject, setEmailSubject] = useState('');\n    const [emailBody, setEmailBody] = useState('');\n    const [message, setMessage] = useState('');\n\n    useEffect(() => {\n        fetch('/admin/cas')\n            .then(res => res.json())\n            .then(data => setCas(data));\n\n        fetch('/admin/referrals')\n            .then(res => res.json())\n            .then(data => setReferrals(data));\n    }, []);\n\n    const handleApprove = (email, approve) => {\n        fetch('/admin/approve-ca', {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify({ email, approve }),\n        }).then(res => res.json())\n          .then(data => {\n              setMessage(data.message);\n              // Refresh CA list\n              fetch('/admin/cas')\n                .then(res => res.json())\n                .then(data => setCas(data));\n          });\n    };\n\n    const handleSendEmail = (e) => {\n        e.preventDefault();\n        const recipients = cas.map(ca => ca.email);\n        fetch('/admin/send-email', {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify({ subject: emailSubject, body: emailBody, recipients }),\n        }).then(res => res.json())\n          .then(data => setMessage(data.message));\n    };\n\n    return (\n        <div>\n            <h1>Admin Panel</h1>\n            {message && <p>{message}</p>}\n\n            <section>\n                <h2>Campus Ambassadors</h2>\n                <ul>\n                    {cas.map((ca, idx) => (\n                        <li key={idx}>\n                            {ca.name} ({ca.email}) - {ca.status}\n                            {ca.status === 'pending' && (\n                                <>\n                                    <button onClick={() => handleApprove(ca.email, true)}>Approve</button>\n                                    <button onClick={() => handleApprove(ca.email, false)}>Disqualify</button>\n                                </>\n                            )}\n                        </li>\n                    ))}\n                </ul>\n            </section>\n\n            <section>\n                <h2>Referral Logs</h2>\n                <ul>\n                    {referrals.map((ref, idx) => (\n                        <li key={idx}>\n                            {ref.referrer_email} referred {ref.referred_email} on {new Date(ref.timestamp).toLocaleString()}\n                        </li>\n                    ))}\n                </ul>\n            </section>\n\n            <section>\n                <h2>Send Bulk Email</h2>\n                <form onSubmit={handleSendEmail}>\n                    <input\n                        type=\"text\"\n                        placeholder=\"Subject\"\n                        value={emailSubject}\n                        onChange={(e) => setEmailSubject(e.target.value)}\n                        required\n                    />\n                    <textarea\n                        placeholder=\"Email body\"\n                        value={emailBody}\n                        onChange={(e) => setEmailBody(e.target.value)}\n                        required\n                    />\n                    <button type=\"submit\">Send Email</button>\n                </form>\n            </section>\n        </div>\n    );\n}\n"], "mappings": "AAAA,YAAY;;AAAA,IAAAA,YAAA;EAAAC,EAAA,GAAAC,YAAA;AACZ,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnD,eAAe,SAASC,YAAYA,CAAA,EAAG;EAAAT,EAAA;EACnC,MAAM,CAACU,GAAG,EAAEC,MAAM,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAE1CD,SAAS,CAAC,MAAM;IACZiB,KAAK,CAAC,YAAY,CAAC,CACdC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAIb,MAAM,CAACa,IAAI,CAAC,CAAC;IAE/BJ,KAAK,CAAC,kBAAkB,CAAC,CACpBC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAIX,YAAY,CAACW,IAAI,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,aAAa,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IACtCP,KAAK,CAAC,mBAAmB,EAAE;MACvBQ,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEN,KAAK;QAAEC;MAAQ,CAAC;IAC3C,CAAC,CAAC,CAACN,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;MACVL,UAAU,CAACK,IAAI,CAACN,OAAO,CAAC;MACxB;MACAE,KAAK,CAAC,YAAY,CAAC,CAChBC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAIb,MAAM,CAACa,IAAI,CAAC,CAAC;IACjC,CAAC,CAAC;EACR,CAAC;EAED,MAAMS,eAAe,GAAIC,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,UAAU,GAAG1B,GAAG,CAAC2B,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACZ,KAAK,CAAC;IAC1CN,KAAK,CAAC,mBAAmB,EAAE;MACvBQ,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEO,OAAO,EAAEzB,YAAY;QAAEgB,IAAI,EAAEd,SAAS;QAAEoB;MAAW,CAAC;IAC/E,CAAC,CAAC,CAACf,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAIL,UAAU,CAACK,IAAI,CAACN,OAAO,CAAC,CAAC;EAC7C,CAAC;EAED,oBACIZ,OAAA;IAAAkC,QAAA,gBACIlC,OAAA;MAAAkC,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAA1C,YAAA;MAAA2C,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACnBzB,OAAO,iBAAIZ,OAAA;MAAAkC,QAAA,EAAItB;IAAO;MAAAuB,QAAA,EAAA1C,YAAA;MAAA2C,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE5BrC,OAAA;MAAAkC,QAAA,gBACIlC,OAAA;QAAAkC,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAA1C,YAAA;QAAA2C,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BrC,OAAA;QAAAkC,QAAA,EACK9B,GAAG,CAAC2B,GAAG,CAAC,CAACC,EAAE,EAAEM,GAAG,kBACbtC,OAAA;UAAAkC,QAAA,GACKF,EAAE,CAACO,IAAI,EAAC,IAAE,EAACP,EAAE,CAACZ,KAAK,EAAC,MAAI,EAACY,EAAE,CAACQ,MAAM,EAClCR,EAAE,CAACQ,MAAM,KAAK,SAAS,iBACpBxC,OAAA,CAAAE,SAAA;YAAAgC,QAAA,gBACIlC,OAAA;cAAQyC,OAAO,EAAEA,CAAA,KAAMtB,aAAa,CAACa,EAAE,CAACZ,KAAK,EAAE,IAAI,CAAE;cAAAc,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAA1C,YAAA;cAAA2C,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtErC,OAAA;cAAQyC,OAAO,EAAEA,CAAA,KAAMtB,aAAa,CAACa,EAAE,CAACZ,KAAK,EAAE,KAAK,CAAE;cAAAc,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAA1C,YAAA;cAAA2C,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eAC5E,CACL;QAAA,GAPIC,GAAG;UAAAH,QAAA,EAAA1C,YAAA;UAAA2C,UAAA;UAAAC,YAAA;QAAA,OAQR,CACP;MAAC;QAAAF,QAAA,EAAA1C,YAAA;QAAA2C,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAF,QAAA,EAAA1C,YAAA;MAAA2C,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEVrC,OAAA;MAAAkC,QAAA,gBACIlC,OAAA;QAAAkC,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAA1C,YAAA;QAAA2C,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBrC,OAAA;QAAAkC,QAAA,EACK5B,SAAS,CAACyB,GAAG,CAAC,CAACW,GAAG,EAAEJ,GAAG,kBACpBtC,OAAA;UAAAkC,QAAA,GACKQ,GAAG,CAACC,cAAc,EAAC,YAAU,EAACD,GAAG,CAACE,cAAc,EAAC,MAAI,EAAC,IAAIC,IAAI,CAACH,GAAG,CAACI,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;QAAA,GAD1FT,GAAG;UAAAH,QAAA,EAAA1C,YAAA;UAAA2C,UAAA;UAAAC,YAAA;QAAA,OAER,CACP;MAAC;QAAAF,QAAA,EAAA1C,YAAA;QAAA2C,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAF,QAAA,EAAA1C,YAAA;MAAA2C,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEVrC,OAAA;MAAAkC,QAAA,gBACIlC,OAAA;QAAAkC,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAA1C,YAAA;QAAA2C,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBrC,OAAA;QAAMgD,QAAQ,EAAErB,eAAgB;QAAAO,QAAA,gBAC5BlC,OAAA;UACIiD,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,SAAS;UACrBC,KAAK,EAAE3C,YAAa;UACpB4C,QAAQ,EAAGxB,CAAC,IAAKnB,eAAe,CAACmB,CAAC,CAACyB,MAAM,CAACF,KAAK,CAAE;UACjDG,QAAQ;QAAA;UAAAnB,QAAA,EAAA1C,YAAA;UAAA2C,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACFrC,OAAA;UACIkD,WAAW,EAAC,YAAY;UACxBC,KAAK,EAAEzC,SAAU;UACjB0C,QAAQ,EAAGxB,CAAC,IAAKjB,YAAY,CAACiB,CAAC,CAACyB,MAAM,CAACF,KAAK,CAAE;UAC9CG,QAAQ;QAAA;UAAAnB,QAAA,EAAA1C,YAAA;UAAA2C,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACFrC,OAAA;UAAQiD,IAAI,EAAC,QAAQ;UAAAf,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAA1C,YAAA;UAAA2C,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAF,QAAA,EAAA1C,YAAA;QAAA2C,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAF,QAAA,EAAA1C,YAAA;MAAA2C,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAF,QAAA,EAAA1C,YAAA;IAAA2C,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEd;AAAC3C,EAAA,CAjGuBS,YAAY;AAAAoD,EAAA,GAAZpD,YAAY;AAAA,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}