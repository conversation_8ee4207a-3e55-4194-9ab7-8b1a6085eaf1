<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Admin Panel - Campus Ambassador</title>
<style>
  *, *::before, *::after {
    box-sizing: border-box;
  }
  body {
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8fafc;
    color: #1e293b;
  }
  .login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 1rem;
  }
  .login-box {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 3rem;
    width: 100%;
    max-width: 400px;
  }
  .login-header {
    text-align: center;
    margin-bottom: 2rem;
  }
  .login-header h1 {
    color: #1e293b;
    margin: 0 0 0.5rem;
  }
  .form-group {
    margin-bottom: 1.5rem;
  }
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
  }
  input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.3s;
  }
  input:focus {
    outline: none;
    border-color: #3b82f6;
  }
  .btn-primary {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    transition: background 0.3s;
  }
  .btn-primary:hover {
    background: #2563eb;
  }
  .btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  .message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    font-weight: 500;
  }
  .error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
  }
  .back-link {
    display: inline-block;
    margin-bottom: 1rem;
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
  }
  .back-link:hover {
    text-decoration: underline;
  }
  /* Admin Dashboard Styles */
  .admin-container {
    display: none;
    min-height: 100vh;
  }
  .admin-header {
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .admin-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
  }
  .logout-btn {
    background: #ef4444;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 500;
  }
  .logout-btn:hover {
    background: #dc2626;
  }
  .admin-content {
    padding: 2rem;
  }
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  .stat-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
  }
  .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #3b82f6;
  }
  .stat-label {
    color: #64748b;
    font-weight: 500;
  }
  .section {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
  }
  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1e293b;
  }
  .table-container {
    overflow-x: auto;
  }
  table {
    width: 100%;
    border-collapse: collapse;
  }
  th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }
  th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
  }
  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
  }
  .status-active {
    background: #d1fae5;
    color: #065f46;
  }
  .status-pending {
    background: #fef3c7;
    color: #92400e;
  }
  .action-btn {
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    cursor: pointer;
    margin-right: 0.5rem;
  }
  .btn-approve {
    background: #22c55e;
    color: white;
  }
  .btn-reject {
    background: #ef4444;
    color: white;
  }
  .btn-download {
    background: #3b82f6;
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 1rem;
  }
  @media (max-width: 768px) {
    .admin-header {
      padding: 1rem;
      flex-direction: column;
      gap: 1rem;
    }
    .admin-content {
      padding: 1rem;
    }
    .stats-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
</head>
<body>

<!-- Login Form -->
<div class="login-container" id="loginContainer">
  <div class="login-box">
    <a href="landing.html" class="back-link">← Back to Home</a>
    
    <div class="login-header">
      <h1>Admin Login</h1>
      <p>Access the Campus Ambassador admin panel</p>
    </div>

    <div id="loginMessage"></div>

    <form id="loginForm">
      <div class="form-group">
        <label for="userid">User ID</label>
        <input type="text" id="userid" name="userid" value="admin" required>
      </div>
      
      <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" value="admin123" required>
      </div>
      
      <button type="submit" class="btn-primary" id="loginBtn">
        Login
      </button>
    </form>
  </div>
</div>

<!-- Admin Dashboard -->
<div class="admin-container" id="adminContainer">
  <div class="admin-header">
    <div class="admin-title">Campus Ambassador Admin Panel</div>
    <button class="logout-btn" onclick="logout()">Logout</button>
  </div>

  <div class="admin-content">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-number" id="totalCAs">0</div>
        <div class="stat-label">Total Campus Ambassadors</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="activeCAs">0</div>
        <div class="stat-label">Active Ambassadors</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="totalReferrals">0</div>
        <div class="stat-label">Total Referrals</div>
      </div>
      <div class="stat-card">
        <div class="stat-number" id="pendingApprovals">0</div>
        <div class="stat-label">Pending Approvals</div>
      </div>
    </div>

    <div class="section">
      <div class="section-title">Campus Ambassadors</div>
      <button class="btn-download" onclick="downloadData()">📥 Download CSV</button>
      <div class="table-container">
        <table id="caTable">
          <thead>
            <tr>
              <th>CA ID</th>
              <th>Name</th>
              <th>Email</th>
              <th>College</th>
              <th>Status</th>
              <th>Referrals</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="caTableBody">
            <tr>
              <td colspan="7" style="text-align: center; padding: 2rem;">Loading...</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="section">
      <div class="section-title">Recent Referral Activity</div>
      <div class="table-container">
        <table id="referralTable">
          <thead>
            <tr>
              <th>Date</th>
              <th>Referrer</th>
              <th>New Registration</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody id="referralTableBody">
            <tr>
              <td colspan="4" style="text-align: center; padding: 2rem;">Loading...</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
// Login functionality
document.getElementById('loginForm').addEventListener('submit', async (e) => {
  e.preventDefault();
  
  const loginBtn = document.getElementById('loginBtn');
  const messageDiv = document.getElementById('loginMessage');
  
  const formData = new FormData();
  formData.append('userid', document.getElementById('userid').value);
  formData.append('password', document.getElementById('password').value);

  loginBtn.disabled = true;
  messageDiv.innerHTML = '';

  try {
    const response = await fetch('http://localhost:8000/admin/login', {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      document.getElementById('loginContainer').style.display = 'none';
      document.getElementById('adminContainer').style.display = 'block';
      loadDashboardData();
    } else {
      const data = await response.json();
      messageDiv.innerHTML = `<div class="message error">${data.detail || 'Login failed'}</div>`;
    }
  } catch (error) {
    messageDiv.innerHTML = `<div class="message error">Network error: ${error.message}</div>`;
  } finally {
    loginBtn.disabled = false;
  }
});

// Load dashboard data
async function loadDashboardData() {
  try {
    // Load CA registrations
    const response = await fetch('http://localhost:8000/admin/registrations');
    if (response.ok) {
      const data = await response.json();
      updateStats(data);
      updateCATable(data);
      updateReferralTable(data);
    }
  } catch (error) {
    console.error('Error loading dashboard data:', error);
  }
}

function updateStats(data) {
  document.getElementById('totalCAs').textContent = data.length;
  document.getElementById('activeCAs').textContent = data.filter(ca => ca.status === 'active').length;
  document.getElementById('totalReferrals').textContent = data.reduce((sum, ca) => sum + (ca.referral_count || 0), 0);
  document.getElementById('pendingApprovals').textContent = data.filter(ca => ca.status === 'pending').length;
}

function updateCATable(data) {
  const tbody = document.getElementById('caTableBody');
  
  if (data.length === 0) {
    tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 2rem;">No campus ambassadors yet</td></tr>';
    return;
  }

  tbody.innerHTML = data.map(ca => `
    <tr>
      <td>CA${ca.id}</td>
      <td>${ca.name}</td>
      <td>${ca.email}</td>
      <td>${ca.college}</td>
      <td><span class="status-badge ${ca.status === 'active' ? 'status-active' : 'status-pending'}">${ca.status || 'pending'}</span></td>
      <td>${ca.referral_count || 0}</td>
      <td>
        ${ca.status !== 'active' ? `<button class="action-btn btn-approve" onclick="updateCAStatus(${ca.id}, 'active')">Approve</button>` : ''}
        <button class="action-btn btn-reject" onclick="updateCAStatus(${ca.id}, 'rejected')">Reject</button>
      </td>
    </tr>
  `).join('');
}

function updateReferralTable(data) {
  const tbody = document.getElementById('referralTableBody');
  
  // Mock referral data for now
  tbody.innerHTML = `
    <tr>
      <td colspan="4" style="text-align: center; padding: 2rem; color: #64748b;">
        Referral tracking will be implemented when referral system is active
      </td>
    </tr>
  `;
}

async function updateCAStatus(caId, status) {
  try {
    const response = await fetch(`http://localhost:8000/admin/ca/${caId}/status`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ status })
    });

    if (response.ok) {
      loadDashboardData(); // Refresh data
    } else {
      alert('Failed to update status');
    }
  } catch (error) {
    alert('Error updating status: ' + error.message);
  }
}

function downloadData() {
  // This will be implemented to download CSV data
  alert('CSV download feature will be implemented in the backend');
}

function logout() {
  document.getElementById('adminContainer').style.display = 'none';
  document.getElementById('loginContainer').style.display = 'flex';
  document.getElementById('loginForm').reset();
}

// Auto-refresh dashboard every 30 seconds
setInterval(() => {
  if (document.getElementById('adminContainer').style.display === 'block') {
    loadDashboardData();
  }
}, 30000);
</script>

</body>
</html>
