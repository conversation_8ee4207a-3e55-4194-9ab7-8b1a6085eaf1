<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Welcome - Campus Ambassador</title>
<style>
  *, *::before, *::after {
    box-sizing: border-box;
  }
  body {
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
  }
  .container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 3rem;
    width: 100%;
    max-width: 700px;
    text-align: center;
  }
  .success-icon {
    font-size: 4rem;
    color: #22c55e;
    margin-bottom: 1rem;
  }
  h1 {
    color: #1e293b;
    font-size: 2.5rem;
    margin: 0 0 1rem;
  }
  .subtitle {
    color: #64748b;
    font-size: 1.2rem;
    margin-bottom: 2rem;
  }
  .ca-info {
    background: #f8fafc;
    border-radius: 0.75rem;
    padding: 2rem;
    margin: 2rem 0;
    border-left: 4px solid #3b82f6;
  }
  .ca-id {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
  }
  .referral-section {
    background: #fef3c7;
    border-radius: 0.75rem;
    padding: 2rem;
    margin: 2rem 0;
    border: 2px solid #f59e0b;
  }
  .referral-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #92400e;
    margin-bottom: 1rem;
  }
  .referral-link {
    background: white;
    border: 2px solid #d97706;
    border-radius: 0.5rem;
    padding: 1rem;
    font-family: monospace;
    font-size: 1rem;
    word-break: break-all;
    margin-bottom: 1rem;
    color: #1e293b;
  }
  .copy-btn {
    background: #f59e0b;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s;
  }
  .copy-btn:hover {
    background: #d97706;
  }
  .copy-btn.copied {
    background: #22c55e;
  }
  .instructions {
    background: #eff6ff;
    border-radius: 0.75rem;
    padding: 2rem;
    margin: 2rem 0;
    text-align: left;
  }
  .instructions h3 {
    color: #1e40af;
    margin-top: 0;
  }
  .instructions ul {
    color: #374151;
    line-height: 1.6;
  }
  .instructions li {
    margin-bottom: 0.5rem;
  }
  .actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
  }
  .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    transition: transform 0.2s;
    cursor: pointer;
    border: none;
  }
  .btn:hover {
    transform: translateY(-2px);
  }
  .btn-primary {
    background: #3b82f6;
    color: white;
  }
  .btn-secondary {
    background: #e5e7eb;
    color: #374151;
  }
  @media (max-width: 600px) {
    .container {
      padding: 2rem 1.5rem;
    }
    h1 {
      font-size: 2rem;
    }
    .actions {
      flex-direction: column;
    }
    .referral-link {
      font-size: 0.9rem;
    }
  }
</style>
</head>
<body>

<div class="container">
  <div class="success-icon">🎉</div>
  <h1>Welcome Aboard!</h1>
  <p class="subtitle">Congratulations! You're now officially a Campus Ambassador for Secura Trainings.</p>

  <div class="ca-info">
    <div class="ca-id" id="caId">Loading...</div>
    <p>Your Campus Ambassador ID</p>
  </div>

  <div class="referral-section">
    <div class="referral-title">🔗 Your Unique Referral Link</div>
    <div class="referral-link" id="referralLink">Loading...</div>
    <button class="copy-btn" id="copyBtn" onclick="copyReferralLink()">
      📋 Copy Link
    </button>
  </div>

  <div class="instructions">
    <h3>🚀 Next Steps:</h3>
    <ul>
      <li><strong>Share your referral link</strong> with friends, classmates, and on social media</li>
      <li><strong>Earn rewards</strong> for every successful registration through your link</li>
      <li><strong>Promote Secura Trainings</strong> programs in your college and network</li>
      <li><strong>Join our WhatsApp group</strong> for updates and coordination (link will be shared via email)</li>
      <li><strong>Check your email</strong> for detailed guidelines and promotional materials</li>
    </ul>
  </div>

  <div class="actions">
    <a href="landing.html" class="btn btn-secondary">Back to Home</a>
    <button class="btn btn-primary" onclick="shareReferralLink()">Share Now</button>
  </div>
</div>

<script>
// Load CA data from localStorage
window.addEventListener('DOMContentLoaded', () => {
  const caData = localStorage.getItem('caData');
  
  if (caData) {
    const data = JSON.parse(caData);
    document.getElementById('caId').textContent = data.ca_id || 'CA' + data.id;
    
    // Generate referral link
    const referralLink = `${window.location.origin}/landing.html?ref=${data.ca_id || 'CA' + data.id}`;
    document.getElementById('referralLink').textContent = referralLink;
  } else {
    // Fallback if no data
    document.getElementById('caId').textContent = 'CA001';
    document.getElementById('referralLink').textContent = `${window.location.origin}/landing.html?ref=CA001`;
  }
});

function copyReferralLink() {
  const referralLink = document.getElementById('referralLink').textContent;
  const copyBtn = document.getElementById('copyBtn');
  
  navigator.clipboard.writeText(referralLink).then(() => {
    copyBtn.textContent = '✅ Copied!';
    copyBtn.classList.add('copied');
    
    setTimeout(() => {
      copyBtn.textContent = '📋 Copy Link';
      copyBtn.classList.remove('copied');
    }, 2000);
  }).catch(() => {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = referralLink;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    
    copyBtn.textContent = '✅ Copied!';
    copyBtn.classList.add('copied');
    
    setTimeout(() => {
      copyBtn.textContent = '📋 Copy Link';
      copyBtn.classList.remove('copied');
    }, 2000);
  });
}

function shareReferralLink() {
  const referralLink = document.getElementById('referralLink').textContent;
  const caId = document.getElementById('caId').textContent;
  
  const shareText = `🎓 Join Secura Trainings Campus Ambassador Program! 

I'm now a Campus Ambassador and you can join too! 

✨ Benefits:
• Cash incentives for referrals
• Internship certificate
• Networking opportunities
• Promotional perks

Apply now: ${referralLink}

#CampusAmbassador #SecuraTrainings #StudentOpportunity`;

  if (navigator.share) {
    navigator.share({
      title: 'Join Campus Ambassador Program',
      text: shareText,
      url: referralLink
    });
  } else {
    // Fallback - copy to clipboard
    navigator.clipboard.writeText(shareText).then(() => {
      alert('Share text copied to clipboard! You can now paste it on social media.');
    });
  }
}

// Clear stored data after successful display
setTimeout(() => {
  localStorage.removeItem('caData');
}, 5000);
</script>

</body>
</html>
