<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Campus Ambassador Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; }
        input { padding: 8px; width: 300px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .message { margin: 15px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>Campus Ambassador Program - Test Page</h1>
    
    <div class="section">
        <h2>Admin Login Test</h2>
        <form id="loginForm">
            <div class="form-group">
                <label for="userid">User ID:</label>
                <input type="text" id="userid" value="admin" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="admin123" required>
            </div>
            <button type="submit">Login</button>
        </form>
        <div id="loginMessage"></div>
    </div>

    <div class="section">
        <h2>Registration Test</h2>
        <form id="registerForm">
            <div class="form-group">
                <label for="name">Name:</label>
                <input type="text" id="name" value="Test User" required>
            </div>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="mobile">Mobile:</label>
                <input type="tel" id="mobile" value="1234567890" required>
            </div>
            <div class="form-group">
                <label for="college">College:</label>
                <input type="text" id="college" value="Test College" required>
            </div>
            <div class="form-group">
                <label for="branch">Branch:</label>
                <input type="text" id="branch" value="CSE" required>
            </div>
            <div class="form-group">
                <label for="year_of_study">Year of Study:</label>
                <input type="text" id="year_of_study" value="3" required>
            </div>
            <button type="submit">Register</button>
        </form>
        <div id="registerMessage"></div>
    </div>

    <script>
        // Admin Login Test
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const userid = document.getElementById('userid').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('loginMessage');
            
            try {
                const formData = new FormData();
                formData.append('userid', userid);
                formData.append('password', password);
                
                const response = await fetch('http://localhost:8000/admin/login', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    messageDiv.innerHTML = '<div class="message success">Login successful!</div>';
                } else {
                    messageDiv.innerHTML = '<div class="message error">Login failed: ' + (data.detail || 'Unknown error') + '</div>';
                }
            } catch (error) {
                messageDiv.innerHTML = '<div class="message error">Network error: ' + error.message + '</div>';
            }
        });

        // Registration Test
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const messageDiv = document.getElementById('registerMessage');
            
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                mobile: document.getElementById('mobile').value,
                college: document.getElementById('college').value,
                branch: document.getElementById('branch').value,
                year_of_study: document.getElementById('year_of_study').value
            };
            
            try {
                const response = await fetch('http://localhost:8000/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    messageDiv.innerHTML = '<div class="message success">Registration successful! ID: ' + data.id + '</div>';
                } else {
                    messageDiv.innerHTML = '<div class="message error">Registration failed: ' + (data.detail || 'Unknown error') + '</div>';
                }
            } catch (error) {
                messageDiv.innerHTML = '<div class="message error">Network error: ' + error.message + '</div>';
            }
        });
    </script>
</body>
</html>
