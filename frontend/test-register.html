<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Test Registration</title>
<style>
  body {
    font-family: Arial, sans-serif;
    max-width: 600px;
    margin: 50px auto;
    padding: 20px;
  }
  .form-group {
    margin-bottom: 15px;
  }
  label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }
  input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
  }
  button {
    background: #007bff;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
  }
  .message {
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
  }
  .success {
    background: #d4edda;
    color: #155724;
  }
  .error {
    background: #f8d7da;
    color: #721c24;
  }
</style>
</head>
<body>

<h1>Test Campus Ambassador Registration</h1>

<form id="testForm">
  <div class="form-group">
    <label for="name">Name:</label>
    <input type="text" id="name" value="<PERSON>" required>
  </div>
  
  <div class="form-group">
    <label for="email">Email:</label>
    <input type="email" id="email" value="<EMAIL>" required>
  </div>
  
  <div class="form-group">
    <label for="mobile">Mobile:</label>
    <input type="tel" id="mobile" value="9876543210" required>
  </div>
  
  <div class="form-group">
    <label for="college">College:</label>
    <input type="text" id="college" value="Delhi University" required>
  </div>
  
  <div class="form-group">
    <label for="branch">Branch:</label>
    <input type="text" id="branch" value="Computer Science" required>
  </div>
  
  <div class="form-group">
    <label for="year_of_study">Year:</label>
    <input type="text" id="year_of_study" value="3rd Year" required>
  </div>
  
  <div class="form-group">
    <label for="linkedin">LinkedIn (Optional):</label>
    <input type="url" id="linkedin" value="https://linkedin.com/in/alexjohnson">
  </div>
  
  <div class="form-group">
    <label for="instagram">Instagram (Optional):</label>
    <input type="text" id="instagram" value="@alex_codes">
  </div>
  
  <button type="submit">Register</button>
</form>

<div id="result"></div>

<script>
document.getElementById('testForm').addEventListener('submit', async (e) => {
  e.preventDefault();
  
  const resultDiv = document.getElementById('result');
  
  // Get form data
  const formData = {
    name: document.getElementById('name').value.trim(),
    email: document.getElementById('email').value.trim(),
    mobile: document.getElementById('mobile').value.trim(),
    college: document.getElementById('college').value.trim(),
    branch: document.getElementById('branch').value.trim(),
    year_of_study: document.getElementById('year_of_study').value.trim(),
    linkedin: document.getElementById('linkedin').value.trim() || null,
    instagram: document.getElementById('instagram').value.trim() || null,
    referral_code: null
  };

  console.log('Sending data:', formData);
  
  try {
    const response = await fetch('http://localhost:8000/ca/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData)
    });

    console.log('Response status:', response.status);
    const data = await response.json();
    console.log('Response data:', data);

    if (response.ok) {
      resultDiv.innerHTML = `
        <div class="message success">
          <h3>Registration Successful!</h3>
          <p><strong>CA ID:</strong> ${data.ca_id}</p>
          <p><strong>Name:</strong> ${data.name}</p>
          <p><strong>Email:</strong> ${data.email}</p>
          <p><strong>Referral Link:</strong> http://localhost:3004/landing.html?ref=${data.ca_id}</p>
        </div>
      `;
    } else {
      resultDiv.innerHTML = `
        <div class="message error">
          <h3>Registration Failed</h3>
          <p>${data.detail || 'Unknown error'}</p>
        </div>
      `;
    }
  } catch (error) {
    console.error('Network error:', error);
    resultDiv.innerHTML = `
      <div class="message error">
        <h3>Network Error</h3>
        <p>${error.message}</p>
        <p>Check console for details</p>
      </div>
    `;
  }
});

// Test email validation
document.getElementById('email').addEventListener('input', (e) => {
  const email = e.target.value;
  const isValid = e.target.checkValidity();
  console.log('Email:', email, 'Valid:', isValid);
  
  if (!isValid && email.length > 0) {
    e.target.style.borderColor = 'red';
  } else {
    e.target.style.borderColor = '#ddd';
  }
});
</script>

</body>
</html>
