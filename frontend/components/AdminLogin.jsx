'use client'
import React, { useState } from 'react';

export default function AdminLogin() {
    const [userid, setUserid] = useState('admin');
    const [password, setPassword] = useState('admin123');
    const [message, setMessage] = useState('');

    const handleSubmit = async (e) => {
        e.preventDefault();
        const formData = new FormData();
        formData.append('userid', userid);
        formData.append('password', password);

        try {
            const res = await fetch('/api/admin/login', {
                method: 'POST',
                body: formData,
            });

            if (res.ok) {
                setMessage('Login successful');
                // Redirect to admin panel or update UI accordingly
                window.location.href = '/admin';
            } else {
                const data = await res.json();
                setMessage(data.detail || 'Login failed');
            }
        } catch (error) {
            setMessage('Network error: ' + error.message);
        }
    };

    return (
        <form onSubmit={handleSubmit}>
            <label htmlFor="userid">User ID:</label>
            <input
                id="userid"
                type="text"
                placeholder="User ID"
                value={userid}
                onChange={(e) => setUserid(e.target.value)}
                required
            />
            <label htmlFor="password">Password:</label>
            <input
                id="password"
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
            />
            <button type="submit">Login</button>
            {message && <p>{message}</p>}
        </form>
    );
}
